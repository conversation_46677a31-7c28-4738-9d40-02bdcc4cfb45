{"name": "isotope-ai-time-management", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start --tunnel", "start": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@expo-google-fonts/inter": "^0.4.1", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "expo": "^53.0.0", "expo-application": "^6.1.4", "expo-blur": "~14.1.5", "expo-camera": "~16.1.8", "expo-constants": "~17.1.6", "expo-device": "^7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-router": "~5.1.0", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.523.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-installed-apps": "^0.0.6", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@types/react": "~19.0.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}