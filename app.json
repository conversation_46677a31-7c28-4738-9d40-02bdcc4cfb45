{"expo": {"name": "IsotopeAI Focus", "slug": "isotope-ai-focus", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true}}}, "android": {"permissions": ["android.permission.QUERY_ALL_PACKAGES", "android.permission.GET_TASKS", "android.permission.PACKAGE_USAGE_STATS"], "package": "com.isotopeai.focus"}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser", "expo-device", "expo-application", "@react-native-async-storage/async-storage"], "experiments": {"typedRoutes": true}}}